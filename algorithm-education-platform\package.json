{"name": "algorithm-education-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "lint:fix": "eslint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest --passWithNoTests", "test:watch": "jest --watch --passWithNoTests", "test:coverage": "jest --coverage --passWithNoTests", "test:debug": "jest --verbose --no-cache", "type-check": "tsc --noEmit"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@radix-ui/react-tabs": "^1.1.13", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "next": "15.5.2", "pyodide": "^0.28.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.19.11", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "jest": "^30.1.1", "jest-environment-jsdom": "^30.1.1", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}